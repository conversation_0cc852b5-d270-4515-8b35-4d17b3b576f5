package dev.pigmomo.yhkit2025.utils.productmonitor

import android.annotation.SuppressLint
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringOperationType
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import java.util.*
import kotlin.math.max

/**
 * 监控状态工具类
 * 用于计算监控任务的等待状态和下次执行时间
 */
object MonitoringStatusUtils {

    /**
     * 监控状态数据类
     */
    data class MonitoringStatus(
        val isWaiting: Boolean,              // 是否正在等待中
        val nextExecutionTime: Date?,        // 下次执行时间
        val remainingSeconds: Long,          // 剩余等待秒数
        val statusText: String,              // 状态文本描述
        val statusColor: androidx.compose.ui.graphics.Color // 状态颜色
    )

    /**
     * 获取监控计划的状态信息
     * @param plan 监控计划
     * @param currentTime 当前时间
     * @param isSchedulerRunning 调度器是否正在运行
     * @return 监控状态信息
     */
    fun getMonitoringStatus(
        plan: MonitoringPlanEntity,
        currentTime: Date = Date(),
        isSchedulerRunning: Boolean = true
    ): MonitoringStatus {
        // 检查基本条件
        if (!plan.isEnabled) {
            return MonitoringStatus(
                isWaiting = false,
                nextExecutionTime = null,
                remainingSeconds = 0,
                statusText = "已禁用",
                statusColor = androidx.compose.ui.graphics.Color.Gray
            )
        }

        // 检查调度器状态（仅对非手动执行的计划）
        if (!isSchedulerRunning && plan.operationType != MonitoringOperationType.MANUAL) {
            return MonitoringStatus(
                isWaiting = false,
                nextExecutionTime = null,
                remainingSeconds = 0,
                statusText = "调度器已停止",
                statusColor = androidx.compose.ui.graphics.Color.Red
            )
        }

        // 检查执行次数限制
        if (plan.maxExecutions != -1 && plan.executedCount >= plan.maxExecutions) {
            return MonitoringStatus(
                isWaiting = false,
                nextExecutionTime = null,
                remainingSeconds = 0,
                statusText = "已完成",
                statusColor = androidx.compose.ui.graphics.Color.Green
            )
        }

        // 检查时间范围
        if (plan.startTime != null && currentTime.before(plan.startTime)) {
            val remainingSeconds = (plan.startTime.time - currentTime.time) / 1000
            return MonitoringStatus(
                isWaiting = true,
                nextExecutionTime = plan.startTime,
                remainingSeconds = remainingSeconds,
                statusText = "等待开始时间",
                statusColor = androidx.compose.ui.graphics.Color.Blue
            )
        }

        if (plan.endTime != null && currentTime.after(plan.endTime)) {
            return MonitoringStatus(
                isWaiting = false,
                nextExecutionTime = null,
                remainingSeconds = 0,
                statusText = "已过期",
                statusColor = androidx.compose.ui.graphics.Color.Red
            )
        }

        // 根据操作类型计算状态
        return when (plan.operationType) {
            MonitoringOperationType.INTERVAL -> getIntervalStatus(plan, currentTime)
            MonitoringOperationType.SCHEDULED -> getScheduledStatus(plan, currentTime)
            MonitoringOperationType.MANUAL -> MonitoringStatus(
                isWaiting = false,
                nextExecutionTime = null,
                remainingSeconds = 0,
                statusText = "手动执行",
                statusColor = androidx.compose.ui.graphics.Color.Gray
            )
        }
    }

    /**
     * 获取间隔监控的状态
     */
    private fun getIntervalStatus(plan: MonitoringPlanEntity, currentTime: Date): MonitoringStatus {
        val lastExecuted = plan.lastExecutedAt
        
        if (lastExecuted == null) {
            // 首次执行
            return if (MonitoringScheduleUtils.shouldExecute(plan, currentTime)) {
                MonitoringStatus(
                    isWaiting = false,
                    nextExecutionTime = currentTime,
                    remainingSeconds = 0,
                    statusText = "准备执行",
                    statusColor = androidx.compose.ui.graphics.Color.Green
                )
            } else {
                MonitoringStatus(
                    isWaiting = true,
                    nextExecutionTime = currentTime,
                    remainingSeconds = 0,
                    statusText = "等待条件满足",
                    statusColor = androidx.compose.ui.graphics.Color.Blue
                )
            }
        }

        val timeDiff = (currentTime.time - lastExecuted.time) / 1000 // 转换为秒
        val remainingSeconds = max(0, plan.intervalSeconds - timeDiff)
        val nextExecutionTime = Date(lastExecuted.time + plan.intervalSeconds * 1000L)

        return if (remainingSeconds > 0) {
            MonitoringStatus(
                isWaiting = true,
                nextExecutionTime = nextExecutionTime,
                remainingSeconds = remainingSeconds,
                statusText = formatRemainingTime(remainingSeconds),
                statusColor = androidx.compose.ui.graphics.Color.Blue
            )
        } else {
            // 间隔时间已到，检查是否满足其他执行条件
            val shouldExecute = MonitoringScheduleUtils.shouldExecute(plan, currentTime)
            if (shouldExecute) {
                // 检查是否已经等待执行超过一定时间（避免无限等待）
                val waitingTime = (currentTime.time - nextExecutionTime.time) / 1000
                if (waitingTime > 120) { // 如果等待超过2分钟，重新计算下次执行时间
                    val nextTime = Date(currentTime.time + plan.intervalSeconds * 1000L)
                    MonitoringStatus(
                        isWaiting = true,
                        nextExecutionTime = nextTime,
                        remainingSeconds = plan.intervalSeconds.toLong(),
                        statusText = formatRemainingTime(plan.intervalSeconds.toLong()),
                        statusColor = androidx.compose.ui.graphics.Color.Blue
                    )
                } else {
                    MonitoringStatus(
                        isWaiting = false,
                        nextExecutionTime = currentTime,
                        remainingSeconds = 0,
                        statusText = "准备执行",
                        statusColor = androidx.compose.ui.graphics.Color.Green
                    )
                }
            } else {
                // 计算下次执行时间
                val nextTime = MonitoringScheduleUtils.calculateNextExecutionTime(plan, currentTime)
                MonitoringStatus(
                    isWaiting = true,
                    nextExecutionTime = nextTime ?: nextExecutionTime,
                    remainingSeconds = 0,
                    statusText = "等待条件满足",
                    statusColor = androidx.compose.ui.graphics.Color.Blue
                )
            }
        }
    }

    /**
     * 获取定时监控的状态
     */
    private fun getScheduledStatus(plan: MonitoringPlanEntity, currentTime: Date): MonitoringStatus {
        val nextExecutionTime = MonitoringScheduleUtils.calculateNextExecutionTime(plan, currentTime)
        
        if (nextExecutionTime == null) {
            return MonitoringStatus(
                isWaiting = false,
                nextExecutionTime = null,
                remainingSeconds = 0,
                statusText = "配置错误",
                statusColor = androidx.compose.ui.graphics.Color.Red
            )
        }

        val remainingSeconds = max(0, (nextExecutionTime.time - currentTime.time) / 1000)

        return if (remainingSeconds > 0) {
            MonitoringStatus(
                isWaiting = true,
                nextExecutionTime = nextExecutionTime,
                remainingSeconds = remainingSeconds,
                statusText = formatRemainingTime(remainingSeconds),
                statusColor = androidx.compose.ui.graphics.Color.Blue
            )
        } else {
            // 检查是否满足执行条件
            val shouldExecute = MonitoringScheduleUtils.shouldExecute(plan, currentTime)
            if (shouldExecute) {
                MonitoringStatus(
                    isWaiting = false,
                    nextExecutionTime = currentTime,
                    remainingSeconds = 0,
                    statusText = "准备执行",
                    statusColor = androidx.compose.ui.graphics.Color.Green
                )
            } else {
                // 计算下次执行时间
                val nextTime = MonitoringScheduleUtils.calculateNextExecutionTime(plan, currentTime)
                MonitoringStatus(
                    isWaiting = true,
                    nextExecutionTime = nextTime ?: nextExecutionTime,
                    remainingSeconds = if (nextTime != null) {
                        kotlin.math.max(0, (nextTime.time - currentTime.time) / 1000)
                    } else 0,
                    statusText = if (nextTime != null) {
                        formatRemainingTime(kotlin.math.max(0, (nextTime.time - currentTime.time) / 1000))
                    } else "等待条件满足",
                    statusColor = androidx.compose.ui.graphics.Color.Blue
                )
            }
        }
    }

    /**
     * 格式化剩余时间
     */
    private fun formatRemainingTime(seconds: Long): String {
        return when {
            seconds < 60 -> "${seconds}秒后执行"
            seconds < 3600 -> {
                val minutes = seconds / 60
                val remainingSeconds = seconds % 60
                if (remainingSeconds > 0) {
                    "${minutes}分${remainingSeconds}秒后执行"
                } else {
                    "${minutes}分钟后执行"
                }
            }
            seconds < 86400 -> {
                val hours = seconds / 3600
                val remainingMinutes = (seconds % 3600) / 60
                if (remainingMinutes > 0) {
                    "${hours}小时${remainingMinutes}分后执行"
                } else {
                    "${hours}小时后执行"
                }
            }
            else -> {
                val days = seconds / 86400
                val remainingHours = (seconds % 86400) / 3600
                if (remainingHours > 0) {
                    "${days}天${remainingHours}小时后执行"
                } else {
                    "${days}天后执行"
                }
            }
        }
    }

    /**
     * 格式化下次执行时间
     */
    @SuppressLint("DefaultLocale")
    fun formatNextExecutionTime(nextExecutionTime: Date?): String {
        if (nextExecutionTime == null) {
            return "无"
        }

        val calendar = Calendar.getInstance()
        calendar.time = nextExecutionTime

        val now = Calendar.getInstance()

        return when {
            // 今天
            calendar.get(Calendar.YEAR) == now.get(Calendar.YEAR) &&
            calendar.get(Calendar.DAY_OF_YEAR) == now.get(Calendar.DAY_OF_YEAR) -> {
                String.format("%02d:%02d:%02d",
                    calendar.get(Calendar.HOUR_OF_DAY),
                    calendar.get(Calendar.MINUTE),
                    calendar.get(Calendar.SECOND)
                )
            }
            // 明天
            calendar.get(Calendar.YEAR) == now.get(Calendar.YEAR) &&
            calendar.get(Calendar.DAY_OF_YEAR) == now.get(Calendar.DAY_OF_YEAR) + 1 -> {
                String.format("明天 %02d:%02d:%02d",
                    calendar.get(Calendar.HOUR_OF_DAY),
                    calendar.get(Calendar.MINUTE),
                    calendar.get(Calendar.SECOND)
                )
            }
            // 其他日期
            else -> {
                String.format("%02d-%02d %02d:%02d:%02d",
                    calendar.get(Calendar.MONTH) + 1,
                    calendar.get(Calendar.DAY_OF_MONTH),
                    calendar.get(Calendar.HOUR_OF_DAY),
                    calendar.get(Calendar.MINUTE),
                    calendar.get(Calendar.SECOND)
                )
            }
        }
    }
}
