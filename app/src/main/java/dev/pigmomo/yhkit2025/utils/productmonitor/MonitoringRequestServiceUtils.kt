package dev.pigmomo.yhkit2025.utils.productmonitor

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestService
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity

/**
 * 监控请求服务工具类
 * 用于从监控计划创建和配置RequestService
 */
object MonitoringRequestServiceUtils {

    private const val TAG = "MonitoringRequestServiceUtils"

    /**
     * 从监控计划创建RequestService
     * @param plan 监控计划
     * @return 配置好的RequestService实例
     */
    fun createRequestService(plan: MonitoringPlanEntity): RequestService {
        Log.d(TAG, "为监控计划 ${plan.name} 创建RequestService，服务类型: ${plan.serviceType}")

        // 使用嵌入的账户信息创建RequestService
        val requestService = RequestService.Companion.create(plan.account, plan.serviceType)

        // 配置RequestService的各种参数
        configureRequestService(requestService, plan)

        return requestService
    }

    /**
     * 配置RequestService的参数
     * @param requestService RequestService实例
     * @param plan 监控计划
     */
    fun configureRequestService(requestService: RequestService, plan: MonitoringPlanEntity) {
        Log.d(TAG, "配置RequestService参数，监控计划: ${plan.name}")

        try {
            // 设置XYH业务参数
            if (plan.xyhBizParams.isNotEmpty()) {
                requestService.setXyhBizParams(plan.xyhBizParams)
                Log.d(TAG, "设置XYH业务参数: ${plan.xyhBizParams}")
            }

            // 设置Web XYH业务参数
            if (plan.webXyhBizParams.isNotEmpty()) {
                requestService.setWebXyhBizParams(plan.webXyhBizParams)
                Log.d(TAG, "设置Web XYH业务参数: ${plan.webXyhBizParams}")
            }

            // 设置店铺ID
            if (plan.shopId.isNotEmpty()) {
                requestService.setShopId(plan.shopId)
                Log.d(TAG, "设置店铺ID: ${plan.shopId}")
            }

            // 设置卖家ID
            if (plan.sellerId.isNotEmpty()) {
                requestService.setSellerId(plan.sellerId)
                Log.d(TAG, "设置卖家ID: ${plan.sellerId}")
            }

            // 设置城市ID
            if (plan.cityId.isNotEmpty()) {
                requestService.setCityId(plan.cityId)
                Log.d(TAG, "设置城市ID: ${plan.cityId}")
            }

            // 设置区域
            if (plan.district.isNotEmpty()) {
                requestService.setDistrict(plan.district)
                Log.d(TAG, "设置区域: ${plan.district}")
            }

            Log.d(TAG, "RequestService配置完成")

        } catch (e: Exception) {
            Log.e(TAG, "配置RequestService时发生错误", e)
        }
    }

    /**
     * 更新RequestService的配置
     * @param requestService RequestService实例
     * @param plan 更新后的监控计划
     */
    fun updateRequestServiceConfig(requestService: RequestService, plan: MonitoringPlanEntity) {
        Log.d(TAG, "更新RequestService配置，监控计划: ${plan.name}")

        // 更新账户信息
        requestService.updateConfig(plan.account)

        // 重新配置所有参数
        configureRequestService(requestService, plan)
    }

    /**
     * 验证监控计划的RequestService配置
     * @param plan 监控计划
     * @return 验证结果和错误信息
     */
    fun validateRequestServiceConfig(plan: MonitoringPlanEntity): Pair<Boolean, String> {
        val errors = mutableListOf<String>()

        // 验证服务类型
        if (plan.serviceType.isEmpty()) {
            errors.add("服务类型不能为空")
        } else if (plan.serviceType !in listOf("app", "mini")) {
            errors.add("不支持的服务类型: ${plan.serviceType}")
        }

        // 验证账户信息
        if (plan.account.uid.isEmpty()) {
            errors.add("账户UID不能为空")
        }

        if (plan.account.accessToken.isEmpty()) {
            errors.add("访问令牌不能为空")
        }



        // 根据服务类型验证特定参数
        when (plan.serviceType) {
            "app" -> {
                // APP服务类型的特定验证
                if (plan.xyhBizParams.isEmpty()) {
                    Log.w(TAG, "APP服务类型建议设置XYH业务参数")
                }
            }
            "mini" -> {
                // 小程序服务类型的特定验证
                if (plan.webXyhBizParams.isEmpty()) {
                    Log.w(TAG, "小程序服务类型建议设置Web XYH业务参数")
                }
            }
        }

        val isValid = errors.isEmpty()
        val errorMessage = if (errors.isNotEmpty()) {
            "配置验证失败: ${errors.joinToString("; ")}"
        } else {
            "配置验证通过"
        }

        Log.d(TAG, "监控计划 ${plan.name} 配置验证结果: $errorMessage")

        return Pair(isValid, errorMessage)
    }

    /**
     * 创建监控计划的RequestService配置摘要
     * @param plan 监控计划
     * @return 配置摘要字符串
     */
    fun getConfigSummary(plan: MonitoringPlanEntity): String {
        val summary = buildString {
            appendLine("RequestService配置摘要:")
            appendLine("- 服务类型: ${plan.serviceType}")
            appendLine("- 账户: ${plan.account.phoneNumber} (${plan.account.uid})")

            if (plan.addressId.isNotEmpty()) {
                appendLine("- 地址ID: ${plan.addressId}")
            }

            if (plan.shopId.isNotEmpty()) {
                appendLine("- 店铺ID: ${plan.shopId}")
            }

            if (plan.sellerId.isNotEmpty()) {
                appendLine("- 卖家ID: ${plan.sellerId}")
            }

            if (plan.cityId.isNotEmpty()) {
                appendLine("- 城市ID: ${plan.cityId}")
            }

            if (plan.district.isNotEmpty()) {
                appendLine("- 区域: ${plan.district}")
            }

            if (plan.xyhBizParams.isNotEmpty()) {
                appendLine("- XYH业务参数: ${plan.xyhBizParams.take(50)}${if (plan.xyhBizParams.length > 50) "..." else ""}")
            }

            if (plan.webXyhBizParams.isNotEmpty()) {
                appendLine("- Web XYH业务参数: ${plan.webXyhBizParams.take(50)}${if (plan.webXyhBizParams.length > 50) "..." else ""}")
            }
        }

        return summary.toString()
    }

    /**
     * 比较两个监控计划的RequestService配置差异
     * @param oldPlan 旧的监控计划
     * @param newPlan 新的监控计划
     * @return 差异描述列表
     */
    fun getConfigDifferences(oldPlan: MonitoringPlanEntity, newPlan: MonitoringPlanEntity): List<String> {
        val differences = mutableListOf<String>()

        if (oldPlan.serviceType != newPlan.serviceType) {
            differences.add("服务类型: ${oldPlan.serviceType} -> ${newPlan.serviceType}")
        }

        if (oldPlan.addressId != newPlan.addressId) {
            differences.add("地址ID: ${oldPlan.addressId} -> ${newPlan.addressId}")
        }

        if (oldPlan.shopId != newPlan.shopId) {
            differences.add("店铺ID: ${oldPlan.shopId} -> ${newPlan.shopId}")
        }

        if (oldPlan.sellerId != newPlan.sellerId) {
            differences.add("卖家ID: ${oldPlan.sellerId} -> ${newPlan.sellerId}")
        }

        if (oldPlan.cityId != newPlan.cityId) {
            differences.add("城市ID: ${oldPlan.cityId} -> ${newPlan.cityId}")
        }

        if (oldPlan.district != newPlan.district) {
            differences.add("区域: ${oldPlan.district} -> ${newPlan.district}")
        }

        if (oldPlan.xyhBizParams != newPlan.xyhBizParams) {
            differences.add("XYH业务参数已更改")
        }

        if (oldPlan.webXyhBizParams != newPlan.webXyhBizParams) {
            differences.add("Web XYH业务参数已更改")
        }

        if (oldPlan.account.uid != newPlan.account.uid) {
            differences.add("账户UID: ${oldPlan.account.uid} -> ${newPlan.account.uid}")
        }

        if (oldPlan.account.accessToken != newPlan.account.accessToken) {
            differences.add("访问令牌已更改")
        }

        return differences
    }

    /**
     * 检查RequestService是否需要重新创建
     * @param oldPlan 旧的监控计划
     * @param newPlan 新的监控计划
     * @return 是否需要重新创建
     */
    fun needsRecreation(oldPlan: MonitoringPlanEntity, newPlan: MonitoringPlanEntity): Boolean {
        // 如果服务类型或账户信息发生变化，需要重新创建
        return oldPlan.serviceType != newPlan.serviceType ||
                oldPlan.account.uid != newPlan.account.uid ||
                oldPlan.account.accessToken != newPlan.account.accessToken
    }
}