package dev.pigmomo.yhkit2025.data.repository.productmonitor

import dev.pigmomo.yhkit2025.data.dao.productmonitor.MonitoringPlanDao
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringOperationType
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringType
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * 监控计划仓库实现类
 * 实现监控计划相关的业务逻辑操作
 *
 * @param monitoringPlanDao 监控计划DAO
 */
class MonitoringPlanRepositoryImpl(
    private val monitoringPlanDao: MonitoringPlanDao
) : MonitoringPlanRepository {

    /**
     * 添加新的监控计划
     *
     * @param plan 要添加的监控计划
     * @return 添加结果，成功返回true，失败返回false
     */
    override suspend fun addMonitoringPlan(plan: MonitoringPlanEntity): Boolean {
        return try {
            monitoringPlanDao.insert(plan) > 0
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 批量添加监控计划
     *
     * @param plans 要添加的监控计划列表
     * @return 添加结果，成功返回true，失败返回false
     */
    override suspend fun addMonitoringPlans(plans: List<MonitoringPlanEntity>): Boolean {
        return try {
            monitoringPlanDao.insertAll(plans).isNotEmpty()
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 更新监控计划
     *
     * @param plan 要更新的监控计划
     * @return 更新结果，成功返回true，失败返回false
     */
    override suspend fun updateMonitoringPlan(plan: MonitoringPlanEntity): Boolean {
        return try {
            monitoringPlanDao.update(plan) > 0
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 删除监控计划
     *
     * @param plan 要删除的监控计划
     * @return 删除结果，成功返回true，失败返回false
     */
    override suspend fun deleteMonitoringPlan(plan: MonitoringPlanEntity): Boolean {
        return try {
            monitoringPlanDao.delete(plan) > 0
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 根据ID删除监控计划
     *
     * @param planId 要删除的监控计划ID
     * @return 删除结果，成功返回true，失败返回false
     */
    override suspend fun deleteMonitoringPlanById(planId: Int): Boolean {
        return try {
            monitoringPlanDao.deleteById(planId) > 0
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 删除指定账户的所有监控计划
     *
     * @param accountUid 账户UID
     * @return 删除结果，成功返回true，失败返回false
     */
    override suspend fun deleteMonitoringPlansByAccountUid(accountUid: String): Boolean {
        return try {
            monitoringPlanDao.deleteByAccountUid(accountUid) > 0
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 获取所有监控计划
     *
     * @return 监控计划列表的Flow
     */
    override fun getAllMonitoringPlans(): Flow<List<MonitoringPlanEntity>> {
        return monitoringPlanDao.getAllPlans()
    }

    /**
     * 获取所有已启用的监控计划
     *
     * @return 已启用的监控计划列表的Flow
     */
    override fun getEnabledMonitoringPlans(): Flow<List<MonitoringPlanEntity>> {
        return monitoringPlanDao.getEnabledPlans()
    }

    /**
     * 根据ID获取监控计划
     *
     * @param planId 监控计划ID
     * @return 监控计划，如果不存在则返回null
     */
    override suspend fun getMonitoringPlanById(planId: Int): MonitoringPlanEntity? {
        return try {
            monitoringPlanDao.getPlanById(planId)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    /**
     * 根据账户UID获取监控计划
     *
     * @param accountUid 账户UID
     * @return 监控计划列表的Flow
     */
    override fun getMonitoringPlansByAccountUid(accountUid: String): Flow<List<MonitoringPlanEntity>> {
        return monitoringPlanDao.getPlansByAccountUid(accountUid)
    }

    /**
     * 根据账户手机号获取监控计划
     *
     * @param phoneNumber 账户手机号
     * @return 监控计划列表的Flow
     */
    override fun getMonitoringPlansByAccountPhone(phoneNumber: String): Flow<List<MonitoringPlanEntity>> {
        return monitoringPlanDao.getPlansByAccountPhone(phoneNumber)
    }

    /**
     * 根据监控类型获取监控计划
     *
     * @param type 监控类型
     * @return 监控计划列表的Flow
     */
    override fun getMonitoringPlansByType(type: MonitoringType): Flow<List<MonitoringPlanEntity>> {
        return monitoringPlanDao.getPlansByType(type)
    }

    /**
     * 根据商品ID获取包含该商品的监控计划
     *
     * @param productId 商品ID
     * @return 监控计划列表的Flow
     */
    override fun getMonitoringPlansByProductId(productId: String): Flow<List<MonitoringPlanEntity>> {
        return monitoringPlanDao.getPlansByProductId(productId)
    }

    /**
     * 更新监控计划的最后执行时间
     *
     * @param planId 监控计划ID
     * @param lastExecutedAt 最后执行时间
     * @return 更新结果，成功返回true，失败返回false
     */
    override suspend fun updateLastExecutedTime(planId: Int, lastExecutedAt: Date): Boolean {
        return try {
            monitoringPlanDao.updateLastExecutedTime(planId, lastExecutedAt) > 0
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 更新监控计划的启用状态
     *
     * @param planId 监控计划ID
     * @param isEnabled 是否启用
     * @return 更新结果，成功返回true，失败返回false
     */
    override suspend fun updateEnabledStatus(planId: Int, isEnabled: Boolean): Boolean {
        return try {
            monitoringPlanDao.updateEnabledStatus(planId, isEnabled) > 0
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 获取需要执行的监控计划
     *
     * @return 需要执行的监控计划列表
     */
    override suspend fun getPlansToExecute(): List<MonitoringPlanEntity> {
        return try {
            monitoringPlanDao.getPlansToExecute(Date())
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }

    /**
     * 获取监控计划总数
     *
     * @return 监控计划总数
     */
    override suspend fun getMonitoringPlanCount(): Int {
        return try {
            monitoringPlanDao.getCount()
        } catch (e: Exception) {
            e.printStackTrace()
            0
        }
    }

    /**
     * 根据操作类型获取监控计划
     */
    override fun getMonitoringPlansByOperationType(operationType: MonitoringOperationType): Flow<List<MonitoringPlanEntity>> {
        return monitoringPlanDao.getPlansByOperationType(operationType)
    }

    /**
     * 获取需要执行的间隔监控计划
     */
    override suspend fun getIntervalPlansToExecute(): List<MonitoringPlanEntity> {
        return try {
            monitoringPlanDao.getIntervalPlansToExecute(Date())
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }

    /**
     * 获取需要执行的定时监控计划
     */
    override suspend fun getScheduledPlansToExecute(): List<MonitoringPlanEntity> {
        return try {
            monitoringPlanDao.getScheduledPlansToExecute(Date())
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }

    /**
     * 更新监控计划的操作类型
     */
    override suspend fun updateOperationType(planId: Int, operationType: MonitoringOperationType): Boolean {
        return try {
            monitoringPlanDao.updateOperationType(planId, operationType)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 更新监控计划的间隔时间
     */
    override suspend fun updateIntervalSeconds(planId: Int, intervalSeconds: Int): Boolean {
        return try {
            monitoringPlanDao.updateIntervalSeconds(planId, intervalSeconds)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 更新监控计划的定时配置
     */
    override suspend fun updateScheduledConfig(planId: Int, scheduledConfig: String): Boolean {
        return try {
            monitoringPlanDao.updateScheduledConfig(planId, scheduledConfig)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 更新监控计划的优先级
     */
    override suspend fun updatePriority(planId: Int, priority: Int): Boolean {
        return try {
            monitoringPlanDao.updatePriority(planId, priority)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 增加监控计划的执行次数
     */
    override suspend fun incrementExecutedCount(planId: Int): Boolean {
        return try {
            monitoringPlanDao.incrementExecutedCount(planId)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 重置监控计划的执行次数
     */
    override suspend fun resetExecutedCount(planId: Int): Boolean {
        return try {
            monitoringPlanDao.resetExecutedCount(planId)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 获取高优先级的监控计划
     */
    override fun getHighPriorityPlans(minPriority: Int): Flow<List<MonitoringPlanEntity>> {
        return monitoringPlanDao.getHighPriorityPlans(minPriority)
    }

    /**
     * 获取已完成的监控计划
     */
    override fun getCompletedPlans(): Flow<List<MonitoringPlanEntity>> {
        return monitoringPlanDao.getCompletedPlans()
    }

    /**
     * 更新监控计划的最后执行时间
     */
    override suspend fun updateLastExecutedAt(planId: Int): Boolean {
        return try {
            monitoringPlanDao.updateLastExecutedAt(planId)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
}