package dev.pigmomo.yhkit2025.data.dao.productmonitor

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeType
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * 商品变化记录数据访问对象
 * 提供对商品变化记录的CRUD操作
 */
@Dao
interface ProductChangeRecordDao {

    /**
     * 插入商品变化记录
     * @param record 商品变化记录实体
     * @return 插入的行ID
     */
    @Insert(onConflict = OnConflictStrategy.Companion.REPLACE)
    suspend fun insertChangeRecord(record: ProductChangeRecordEntity): Long

    /**
     * 批量插入商品变化记录
     * @param records 商品变化记录实体列表
     * @return 插入的行ID列表
     */
    @Insert(onConflict = OnConflictStrategy.Companion.REPLACE)
    suspend fun insertChangeRecords(records: List<ProductChangeRecordEntity>): List<Long>

    /**
     * 删除商品变化记录
     * @param record 商品变化记录实体
     */
    @Delete
    suspend fun deleteChangeRecord(record: ProductChangeRecordEntity)

    /**
     * 根据记录ID删除商品变化记录
     * @param recordId 记录ID
     */
    @Query("DELETE FROM product_change_records WHERE id = :recordId")
    suspend fun deleteChangeRecordById(recordId: Long)

    /**
     * 删除指定商品的所有变化记录
     * @param productId 商品ID
     */
    @Query("DELETE FROM product_change_records WHERE product_id = :productId")
    suspend fun deleteChangeRecordsByProductId(productId: String)

    /**
     * 获取所有商品变化记录
     * @return 商品变化记录实体列表的Flow
     */
    @Query("SELECT * FROM product_change_records ORDER BY change_time DESC")
    fun getAllChangeRecords(): Flow<List<ProductChangeRecordEntity>>

    /**
     * 根据商品ID获取变化记录
     * @param productId 商品ID
     * @return 商品变化记录实体列表的Flow
     */
    @Query("SELECT * FROM product_change_records WHERE product_id = :productId AND shop_id = :shopId ORDER BY change_time DESC")
    fun getChangeRecordsByProductId(
        productId: String,
        shopId: String
    ): Flow<List<ProductChangeRecordEntity>>

    /**
     * 根据变化类型获取变化记录
     * @param changeType 变化类型
     * @return 商品变化记录实体列表的Flow
     */
    @Query("SELECT * FROM product_change_records WHERE change_type = :changeType ORDER BY change_time DESC")
    fun getChangeRecordsByType(changeType: ProductChangeType): Flow<List<ProductChangeRecordEntity>>

    /**
     * 根据商品ID和变化类型获取变化记录
     * @param productId 商品ID
     * @param changeType 变化类型
     * @return 商品变化记录实体列表的Flow
     */
    @Query("SELECT * FROM product_change_records WHERE product_id = :productId AND change_type = :changeType ORDER BY change_time DESC")
    fun getChangeRecordsByProductIdAndType(
        productId: String,
        changeType: ProductChangeType
    ): Flow<List<ProductChangeRecordEntity>>

    /**
     * 获取重要变化记录
     * @return 重要商品变化记录实体列表的Flow
     */
    @Query("SELECT * FROM product_change_records WHERE is_important = 1 ORDER BY change_time DESC")
    fun getImportantChangeRecords(): Flow<List<ProductChangeRecordEntity>>

    /**
     * 获取指定时间范围内的变化记录
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 商品变化记录实体列表的Flow
     */
    @Query("SELECT * FROM product_change_records WHERE change_time BETWEEN :startTime AND :endTime ORDER BY change_time DESC")
    fun getChangeRecordsByTimeRange(
        startTime: Date,
        endTime: Date
    ): Flow<List<ProductChangeRecordEntity>>

    /**
     * 获取指定商品在指定时间范围内的变化记录
     * @param productId 商品ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 商品变化记录实体列表的Flow
     */
    @Query("SELECT * FROM product_change_records WHERE product_id = :productId AND change_time BETWEEN :startTime AND :endTime ORDER BY change_time DESC")
    fun getChangeRecordsByProductIdAndTimeRange(
        productId: String,
        startTime: Date,
        endTime: Date
    ): Flow<List<ProductChangeRecordEntity>>

    /**
     * 获取最近的变化记录
     * @param limit 限制数量
     * @return 商品变化记录实体列表的Flow
     */
    @Query("SELECT * FROM product_change_records ORDER BY change_time DESC LIMIT :limit")
    fun getRecentChangeRecords(limit: Int = 100): Flow<List<ProductChangeRecordEntity>>

    /**
     * 获取指定商品的最新变化记录
     * @param productId 商品ID
     * @param changeType 变化类型（可选）
     * @return 商品变化记录实体
     */
    @Query("SELECT * FROM product_change_records WHERE product_id = :productId AND (:changeType IS NULL OR change_type = :changeType) ORDER BY change_time DESC LIMIT 1")
    suspend fun getLatestChangeRecord(
        productId: String,
        changeType: ProductChangeType? = null
    ): ProductChangeRecordEntity?

    /**
     * 获取价格变化记录统计
     * @param productId 商品ID
     * @return 价格变化次数
     */
    @Query("SELECT COUNT(*) FROM product_change_records WHERE product_id = :productId AND change_type = 'PRICE_CHANGE'")
    suspend fun getPriceChangeCount(productId: String): Int

    /**
     * 获取库存变化记录统计
     * @param productId 商品ID
     * @return 库存变化次数
     */
    @Query("SELECT COUNT(*) FROM product_change_records WHERE product_id = :productId AND change_type = 'STOCK_CHANGE'")
    suspend fun getStockChangeCount(productId: String): Int

    /**
     * 清理旧的变化记录（保留最近N天的记录）
     * @param beforeDate 清理此日期之前的记录
     * @return 删除的记录数量
     */
    @Query("DELETE FROM product_change_records WHERE change_time < :beforeDate")
    suspend fun cleanOldRecords(beforeDate: Date): Int

    /**
     * 获取变化记录总数
     * @return 变化记录总数
     */
    @Query("SELECT COUNT(*) FROM product_change_records")
    suspend fun getChangeRecordCount(): Int

    /**
     * 获取指定商品的变化记录总数
     * @param productId 商品ID
     * @return 变化记录总数
     */
    @Query("SELECT COUNT(*) FROM product_change_records WHERE product_id = :productId")
    suspend fun getChangeRecordCountByProductId(productId: String): Int

    /**
     * 根据店铺ID获取变化记录
     * @param shopId 店铺ID
     * @return 商品变化记录实体列表的Flow
     */
    @Query("SELECT * FROM product_change_records WHERE shop_id = :shopId ORDER BY change_time DESC")
    fun getChangeRecordsByShopId(shopId: String): Flow<List<ProductChangeRecordEntity>>

    /**
     * 根据商品ID和店铺ID获取变化记录
     * @param productId 商品ID
     * @param shopId 店铺ID
     * @return 商品变化记录实体列表的Flow
     */
    @Query("SELECT * FROM product_change_records WHERE product_id = :productId AND shop_id = :shopId ORDER BY change_time DESC")
    fun getChangeRecordsByProductIdAndShopId(
        productId: String,
        shopId: String
    ): Flow<List<ProductChangeRecordEntity>>

    /**
     * 删除指定店铺的所有变化记录
     * @param shopId 店铺ID
     */
    @Query("DELETE FROM product_change_records WHERE shop_id = :shopId")
    suspend fun deleteChangeRecordsByShopId(shopId: String)

    /**
     * 删除指定商品在指定店铺的所有变化记录
     * @param productId 商品ID
     * @param shopId 店铺ID
     */
    @Query("DELETE FROM product_change_records WHERE product_id = :productId AND shop_id = :shopId")
    suspend fun deleteChangeRecordsByProductIdAndShopId(productId: String, shopId: String)

    /**
     * 获取指定店铺的变化记录总数
     * @param shopId 店铺ID
     * @return 变化记录总数
     */
    @Query("SELECT COUNT(*) FROM product_change_records WHERE shop_id = :shopId")
    suspend fun getChangeRecordCountByShopId(shopId: String): Int
}