package dev.pigmomo.yhkit2025.data.repository.productmonitor

import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringOperationType
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringType
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * 监控计划仓库接口
 * 定义监控计划相关的业务逻辑操作
 */
interface MonitoringPlanRepository {
    /**
     * 添加新的监控计划
     *
     * @param plan 要添加的监控计划
     * @return 添加结果，成功返回true，失败返回false
     */
    suspend fun addMonitoringPlan(plan: MonitoringPlanEntity): Boolean

    /**
     * 批量添加监控计划
     *
     * @param plans 要添加的监控计划列表
     * @return 添加结果，成功返回true，失败返回false
     */
    suspend fun addMonitoringPlans(plans: List<MonitoringPlanEntity>): Boolean

    /**
     * 更新监控计划
     *
     * @param plan 要更新的监控计划
     * @return 更新结果，成功返回true，失败返回false
     */
    suspend fun updateMonitoringPlan(plan: MonitoringPlanEntity): Boolean

    /**
     * 删除监控计划
     *
     * @param plan 要删除的监控计划
     * @return 删除结果，成功返回true，失败返回false
     */
    suspend fun deleteMonitoringPlan(plan: MonitoringPlanEntity): Boolean

    /**
     * 根据ID删除监控计划
     *
     * @param planId 要删除的监控计划ID
     * @return 删除结果，成功返回true，失败返回false
     */
    suspend fun deleteMonitoringPlanById(planId: Int): Boolean

    /**
     * 删除指定账户的所有监控计划
     *
     * @param accountUid 账户UID
     * @return 删除结果，成功返回true，失败返回false
     */
    suspend fun deleteMonitoringPlansByAccountUid(accountUid: String): Boolean

    /**
     * 获取所有监控计划
     *
     * @return 监控计划列表的Flow
     */
    fun getAllMonitoringPlans(): Flow<List<MonitoringPlanEntity>>

    /**
     * 获取所有已启用的监控计划
     *
     * @return 已启用的监控计划列表的Flow
     */
    fun getEnabledMonitoringPlans(): Flow<List<MonitoringPlanEntity>>

    /**
     * 根据ID获取监控计划
     *
     * @param planId 监控计划ID
     * @return 监控计划，如果不存在则返回null
     */
    suspend fun getMonitoringPlanById(planId: Int): MonitoringPlanEntity?

    /**
     * 根据账户UID获取监控计划
     *
     * @param accountUid 账户UID
     * @return 监控计划列表的Flow
     */
    fun getMonitoringPlansByAccountUid(accountUid: String): Flow<List<MonitoringPlanEntity>>

    /**
     * 根据账户手机号获取监控计划
     *
     * @param phoneNumber 账户手机号
     * @return 监控计划列表的Flow
     */
    fun getMonitoringPlansByAccountPhone(phoneNumber: String): Flow<List<MonitoringPlanEntity>>

    /**
     * 根据监控类型获取监控计划
     *
     * @param type 监控类型
     * @return 监控计划列表的Flow
     */
    fun getMonitoringPlansByType(type: MonitoringType): Flow<List<MonitoringPlanEntity>>

    /**
     * 根据商品ID获取包含该商品的监控计划
     *
     * @param productId 商品ID
     * @return 监控计划列表的Flow
     */
    fun getMonitoringPlansByProductId(productId: String): Flow<List<MonitoringPlanEntity>>

    /**
     * 更新监控计划的最后执行时间
     *
     * @param planId 监控计划ID
     * @param lastExecutedAt 最后执行时间
     * @return 更新结果，成功返回true，失败返回false
     */
    suspend fun updateLastExecutedTime(planId: Int, lastExecutedAt: Date = Date()): Boolean

    /**
     * 更新监控计划的启用状态
     *
     * @param planId 监控计划ID
     * @param isEnabled 是否启用
     * @return 更新结果，成功返回true，失败返回false
     */
    suspend fun updateEnabledStatus(planId: Int, isEnabled: Boolean): Boolean

    /**
     * 获取需要执行的监控计划
     *
     * @return 需要执行的监控计划列表
     */
    suspend fun getPlansToExecute(): List<MonitoringPlanEntity>

    /**
     * 获取监控计划总数
     *
     * @return 监控计划总数
     */
    suspend fun getMonitoringPlanCount(): Int

    /**
     * 根据操作类型获取监控计划
     *
     * @param operationType 监控操作类型
     * @return 监控计划列表的Flow
     */
    fun getMonitoringPlansByOperationType(operationType: MonitoringOperationType): Flow<List<MonitoringPlanEntity>>

    /**
     * 获取需要执行的间隔监控计划
     *
     * @return 需要执行的间隔监控计划列表
     */
    suspend fun getIntervalPlansToExecute(): List<MonitoringPlanEntity>

    /**
     * 获取需要执行的定时监控计划
     *
     * @return 需要执行的定时监控计划列表
     */
    suspend fun getScheduledPlansToExecute(): List<MonitoringPlanEntity>

    /**
     * 更新监控计划的操作类型
     *
     * @param planId 监控计划ID
     * @param operationType 新的操作类型
     * @return 更新结果，成功返回true，失败返回false
     */
    suspend fun updateOperationType(planId: Int, operationType: MonitoringOperationType): Boolean

    /**
     * 更新监控计划的间隔时间
     *
     * @param planId 监控计划ID
     * @param intervalSeconds 新的间隔时间（秒）
     * @return 更新结果，成功返回true，失败返回false
     */
    suspend fun updateIntervalSeconds(planId: Int, intervalSeconds: Int): Boolean

    /**
     * 更新监控计划的定时配置
     *
     * @param planId 监控计划ID
     * @param scheduledConfig 新的定时配置
     * @return 更新结果，成功返回true，失败返回false
     */
    suspend fun updateScheduledConfig(planId: Int, scheduledConfig: String): Boolean

    /**
     * 更新监控计划的优先级
     *
     * @param planId 监控计划ID
     * @param priority 新的优先级
     * @return 更新结果，成功返回true，失败返回false
     */
    suspend fun updatePriority(planId: Int, priority: Int): Boolean

    /**
     * 增加监控计划的执行次数
     *
     * @param planId 监控计划ID
     * @return 更新结果，成功返回true，失败返回false
     */
    suspend fun incrementExecutedCount(planId: Int): Boolean

    /**
     * 重置监控计划的执行次数
     *
     * @param planId 监控计划ID
     * @return 更新结果，成功返回true，失败返回false
     */
    suspend fun resetExecutedCount(planId: Int): Boolean

    /**
     * 获取高优先级的监控计划
     *
     * @param minPriority 最小优先级
     * @return 高优先级监控计划列表的Flow
     */
    fun getHighPriorityPlans(minPriority: Int = 7): Flow<List<MonitoringPlanEntity>>

    /**
     * 获取已完成的监控计划
     *
     * @return 已完成的监控计划列表的Flow
     */
    fun getCompletedPlans(): Flow<List<MonitoringPlanEntity>>

    /**
     * 更新监控计划的最后执行时间
     *
     * @param planId 监控计划ID
     * @return 更新结果，成功返回true，失败返回false
     */
    suspend fun updateLastExecutedAt(planId: Int): Boolean
}