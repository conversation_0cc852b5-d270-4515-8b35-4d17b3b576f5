package dev.pigmomo.yhkit2025.data.dao.productmonitor

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * 商品监控数据访问对象
 * 提供对商品监控数据的CRUD操作
 */
@Dao
interface ProductMonitorDao {

    /**
     * 插入或更新商品监控记录
     * @param product 商品监控实体
     * @return 插入的行ID
     */
    @Insert(onConflict = OnConflictStrategy.Companion.REPLACE)
    suspend fun insertOrUpdateProduct(product: ProductMonitorEntity): Long

    /**
     * 批量插入或更新商品监控记录
     * @param products 商品监控实体列表
     * @return 插入的行ID列表
     */
    @Insert(onConflict = OnConflictStrategy.Companion.REPLACE)
    suspend fun insertOrUpdateProducts(products: List<ProductMonitorEntity>): List<Long>

    /**
     * 更新商品监控记录
     * @param product 商品监控实体
     */
    @Update
    suspend fun updateProduct(product: ProductMonitorEntity)

    /**
     * 删除商品监控记录
     * @param product 商品监控实体
     */
    @Delete
    suspend fun deleteProduct(product: ProductMonitorEntity)

    /**
     * 根据商品ID删除商品监控记录
     * @param productId 商品ID
     */
    @Query("DELETE FROM product_monitor WHERE id = :productId")
    suspend fun deleteProductById(productId: String)

    /**
     * 获取所有商品监控记录
     * @return 商品监控实体列表的Flow
     */
    @Query("SELECT * FROM product_monitor ORDER BY last_update_time DESC")
    fun getAllProducts(): Flow<List<ProductMonitorEntity>>

    /**
     * 获取所有启用监控的商品
     * @return 启用监控的商品实体列表的Flow
     */
    @Query("SELECT * FROM product_monitor WHERE is_monitoring_enabled = 1 ORDER BY last_update_time DESC")
    fun getEnabledProducts(): Flow<List<ProductMonitorEntity>>

    /**
     * 根据商品ID获取商品监控记录
     * @param productId 商品ID
     * @return 商品监控实体的Flow
     */
    @Query("SELECT * FROM product_monitor WHERE id = :productId")
    fun getProductById(productId: String): Flow<ProductMonitorEntity?>

    /**
     * 根据商品ID获取商品监控记录（同步方法）
     * @param productId 商品ID
     * @return 商品监控实体
     */
    @Query("SELECT * FROM product_monitor WHERE id = :productId")
    suspend fun getProductByIdSync(productId: String): ProductMonitorEntity?

    /**
     * 根据店铺ID获取商品列表
     * @param shopId 店铺ID
     * @return 商品监控实体列表的Flow
     */
    @Query("SELECT * FROM product_monitor WHERE shop_id = :shopId ORDER BY last_update_time DESC")
    fun getProductsByShopId(shopId: String): Flow<List<ProductMonitorEntity>>

    /**
     * 根据分类ID获取商品列表
     * @param categoryId 分类ID
     * @return 商品监控实体列表的Flow
     */
    @Query("SELECT * FROM product_monitor WHERE category_id = :categoryId ORDER BY last_update_time DESC")
    fun getProductsByCategoryId(categoryId: String): Flow<List<ProductMonitorEntity>>

    /**
     * 搜索商品（根据标题）
     * @param keyword 搜索关键词
     * @return 商品监控实体列表的Flow
     */
    @Query("SELECT * FROM product_monitor WHERE title LIKE '%' || :keyword || '%' OR subtitle LIKE '%' || :keyword || '%' ORDER BY last_update_time DESC")
    fun searchProducts(keyword: String): Flow<List<ProductMonitorEntity>>

    /**
     * 获取可用的商品列表
     * @return 可用商品实体列表的Flow
     */
    @Query("SELECT * FROM product_monitor WHERE available = 1 AND can_not_buy = 0 ORDER BY last_update_time DESC")
    fun getAvailableProducts(): Flow<List<ProductMonitorEntity>>

    /**
     * 获取秒杀商品列表
     * @return 秒杀商品实体列表的Flow
     */
    @Query("SELECT * FROM product_monitor WHERE is_seckill = 1 ORDER BY last_update_time DESC")
    fun getSeckillProducts(): Flow<List<ProductMonitorEntity>>

    /**
     * 获取缺货商品列表
     * @return 缺货商品实体列表的Flow
     */
    @Query("SELECT * FROM product_monitor WHERE stock_num <= 0 OR available = 0 ORDER BY last_update_time DESC")
    fun getOutOfStockProducts(): Flow<List<ProductMonitorEntity>>

    /**
     * 获取价格变化的商品（最近24小时）
     * @param since 起始时间
     * @return 商品监控实体列表的Flow
     */
    @Query("SELECT * FROM product_monitor WHERE last_update_time >= :since ORDER BY last_update_time DESC")
    fun getRecentlyUpdatedProducts(since: Date): Flow<List<ProductMonitorEntity>>

    /**
     * 更新商品的最后更新时间
     * @param productId 商品ID
     * @param updateTime 更新时间
     */
    @Query("UPDATE product_monitor SET last_update_time = :updateTime WHERE id = :productId")
    suspend fun updateLastUpdateTime(productId: String, updateTime: Date = Date())

    /**
     * 更新商品的监控状态
     * @param productId 商品ID
     * @param isEnabled 是否启用监控
     */
    @Query("UPDATE product_monitor SET is_monitoring_enabled = :isEnabled WHERE id = :productId")
    suspend fun updateMonitoringStatus(productId: String, isEnabled: Boolean)

    /**
     * 获取商品总数
     * @return 商品总数
     */
    @Query("SELECT COUNT(*) FROM product_monitor")
    suspend fun getProductCount(): Int

    /**
     * 获取启用监控的商品总数
     * @return 启用监控的商品总数
     */
    @Query("SELECT COUNT(*) FROM product_monitor WHERE is_monitoring_enabled = 1")
    suspend fun getEnabledProductCount(): Int
}