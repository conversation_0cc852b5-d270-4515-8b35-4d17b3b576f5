package dev.pigmomo.yhkit2025.data.utils

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * Room类型转换器，用于将List<String>转换为可存储的字符串格式
 * 使用Gson进行序列化和反序列化
 */
class StringListConverter {
    private val gson = Gson()
    private val type = object : TypeToken<List<String>>() {}.type
    
    /**
     * 将字符串列表转换为JSON字符串以存储在数据库中
     * 
     * @param value 要转换的字符串列表
     * @return 序列化后的JSON字符串
     */
    @TypeConverter
    fun fromStringList(value: List<String>?): String {
        return if (value == null || value.isEmpty()) {
            "[]"
        } else {
            gson.toJson(value, type)
        }
    }
    
    /**
     * 将数据库中的JSON字符串转换回字符串列表
     * 
     * @param value 存储在数据库中的JSON字符串
     * @return 反序列化后的字符串列表
     */
    @TypeConverter
    fun toStringList(value: String?): List<String> {
        return if (value.isNullOrBlank() || value == "[]") {
            emptyList()
        } else {
            try {
                gson.fromJson(value, type)
            } catch (e: Exception) {
                emptyList()
            }
        }
    }
} 