package dev.pigmomo.yhkit2025.data.repository.productmonitor

import dev.pigmomo.yhkit2025.api.model.cart.Product
import dev.pigmomo.yhkit2025.api.model.product.ProductDetailData
import dev.pigmomo.yhkit2025.data.dao.productmonitor.ProductChangeRecordDao
import dev.pigmomo.yhkit2025.data.dao.productmonitor.ProductMonitorDao
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeType
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.utils.productmonitor.ProductMonitorUtils
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * 商品监控仓库实现类
 * 实现商品监控相关的业务逻辑操作
 */
class ProductMonitorRepositoryImpl(
    private val productMonitorDao: ProductMonitorDao,
    private val productChangeRecordDao: ProductChangeRecordDao
) : ProductMonitorRepository {

    /**
     * 添加或更新商品监控
     */
    override suspend fun addOrUpdateProduct(product: Product, shopId: String): Boolean {
        return try {
            val existingProduct = productMonitorDao.getProductByIdSync(product.id)
            val productEntity = if (existingProduct != null) {
                // 更新现有商品，保留首次添加时间
                ProductMonitorUtils.convertToProductMonitorEntity(product, shopId, false).copy(
                    firstAddTime = existingProduct.firstAddTime,
                    isMonitoringEnabled = existingProduct.isMonitoringEnabled,
                    monitorNote = existingProduct.monitorNote
                )
            } else {
                // 新增商品
                ProductMonitorUtils.convertToProductMonitorEntity(product, shopId, true)
            }

            productMonitorDao.insertOrUpdateProduct(productEntity)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 批量添加或更新商品监控
     */
    override suspend fun addOrUpdateProducts(products: List<Product>, shopId: String): Boolean {
        return try {
            val productEntities = products.map { product ->
                val existingProduct = productMonitorDao.getProductByIdSync(product.id)
                if (existingProduct != null) {
                    // 更新现有商品
                    ProductMonitorUtils.convertToProductMonitorEntity(product, shopId, false).copy(
                        firstAddTime = existingProduct.firstAddTime,
                        isMonitoringEnabled = existingProduct.isMonitoringEnabled,
                        monitorNote = existingProduct.monitorNote
                    )
                } else {
                    // 新增商品
                    ProductMonitorUtils.convertToProductMonitorEntity(product, shopId, true)
                }
            }

            productMonitorDao.insertOrUpdateProducts(productEntities)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 更新商品监控数据并记录变化
     */
    override suspend fun updateProductAndRecordChanges(product: Product, shopId: String): List<ProductChangeRecordEntity> {
        return try {
            val existingProduct = productMonitorDao.getProductByIdSync(product.id)
            val newProductEntity = ProductMonitorUtils.convertToProductMonitorEntity(product, shopId, false)

            val changes = if (existingProduct != null) {
                // 检测变化
                val updatedEntity = newProductEntity.copy(
                    firstAddTime = existingProduct.firstAddTime,
                    isMonitoringEnabled = existingProduct.isMonitoringEnabled,
                    monitorNote = existingProduct.monitorNote
                )

                val detectedChanges = ProductMonitorUtils.detectProductChanges(existingProduct, updatedEntity)

                // 更新商品数据
                productMonitorDao.insertOrUpdateProduct(updatedEntity)

                // 保存变化记录
                if (detectedChanges.isNotEmpty()) {
                    productChangeRecordDao.insertChangeRecords(detectedChanges)
                }

                detectedChanges
            } else {
                // 新商品，直接添加
                productMonitorDao.insertOrUpdateProduct(newProductEntity)
                emptyList()
            }

            changes
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }

    /**
     * 添加或更新商品监控（从商品详情页）
     */
    override suspend fun addOrUpdateProductFromDetail(productDetail: ProductDetailData, shopId: String): Boolean {
        return try {
            val productId = productDetail.id ?: return false
            val existingProduct = productMonitorDao.getProductByIdSync(productId)

            val productEntity = if (existingProduct != null) {
                // 更新现有商品，保留首次添加时间
                ProductMonitorUtils.convertProductDetailToMonitorEntity(productDetail, shopId, false).copy(
                    firstAddTime = existingProduct.firstAddTime,
                    isMonitoringEnabled = existingProduct.isMonitoringEnabled,
                    monitorNote = existingProduct.monitorNote
                )
            } else {
                // 新增商品
                ProductMonitorUtils.convertProductDetailToMonitorEntity(productDetail, shopId, true)
            }

            productMonitorDao.insertOrUpdateProduct(productEntity)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 更新商品监控数据并记录变化（从商品详情页）
     */
    override suspend fun updateProductDetailAndRecordChanges(productDetail: ProductDetailData, shopId: String): List<ProductChangeRecordEntity> {
        return try {
            val productId = productDetail.id ?: return emptyList()
            val existingProduct = productMonitorDao.getProductByIdSync(productId)
            val newProductEntity = ProductMonitorUtils.convertProductDetailToMonitorEntity(productDetail, shopId, false)

            val changes = if (existingProduct != null) {
                // 检测变化
                val updatedEntity = newProductEntity.copy(
                    firstAddTime = existingProduct.firstAddTime,
                    isMonitoringEnabled = existingProduct.isMonitoringEnabled,
                    monitorNote = existingProduct.monitorNote
                )

                val detectedChanges = ProductMonitorUtils.detectProductChanges(existingProduct, updatedEntity)

                // 更新商品数据
                productMonitorDao.insertOrUpdateProduct(updatedEntity)

                // 保存变化记录
                if (detectedChanges.isNotEmpty()) {
                    productChangeRecordDao.insertChangeRecords(detectedChanges)
                }

                detectedChanges
            } else {
                // 新商品，直接添加
                productMonitorDao.insertOrUpdateProduct(newProductEntity)
                emptyList()
            }

            changes
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }

    /**
     * 删除商品监控
     */
    override suspend fun deleteProduct(productId: String): Boolean {
        return try {
            productMonitorDao.deleteProductById(productId)
            // 删除相关的变化记录
            productChangeRecordDao.deleteChangeRecordsByProductId(productId)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 获取所有商品监控
     */
    override fun getAllProducts(): Flow<List<ProductMonitorEntity>> {
        return productMonitorDao.getAllProducts()
    }

    /**
     * 获取启用监控的商品
     */
    override fun getEnabledProducts(): Flow<List<ProductMonitorEntity>> {
        return productMonitorDao.getEnabledProducts()
    }

    /**
     * 根据商品ID获取商品监控
     */
    override fun getProductById(productId: String): Flow<ProductMonitorEntity?> {
        return productMonitorDao.getProductById(productId)
    }

    /**
     * 搜索商品
     */
    override fun searchProducts(keyword: String): Flow<List<ProductMonitorEntity>> {
        return productMonitorDao.searchProducts(keyword)
    }

    /**
     * 获取可用商品
     */
    override fun getAvailableProducts(): Flow<List<ProductMonitorEntity>> {
        return productMonitorDao.getAvailableProducts()
    }

    /**
     * 获取秒杀商品
     */
    override fun getSeckillProducts(): Flow<List<ProductMonitorEntity>> {
        return productMonitorDao.getSeckillProducts()
    }

    /**
     * 获取缺货商品
     */
    override fun getOutOfStockProducts(): Flow<List<ProductMonitorEntity>> {
        return productMonitorDao.getOutOfStockProducts()
    }

    /**
     * 更新商品监控状态
     */
    override suspend fun updateMonitoringStatus(productId: String, isEnabled: Boolean): Boolean {
        return try {
            productMonitorDao.updateMonitoringStatus(productId, isEnabled)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 获取商品变化记录
     */
    override fun getProductChangeRecords(productId: String, shopId: String): Flow<List<ProductChangeRecordEntity>> {
        return productChangeRecordDao.getChangeRecordsByProductId(productId, shopId)
    }

    /**
     * 获取所有变化记录
     */
    override fun getAllChangeRecords(): Flow<List<ProductChangeRecordEntity>> {
        return productChangeRecordDao.getAllChangeRecords()
    }

    /**
     * 获取重要变化记录
     */
    override fun getImportantChangeRecords(): Flow<List<ProductChangeRecordEntity>> {
        return productChangeRecordDao.getImportantChangeRecords()
    }

    /**
     * 获取指定类型的变化记录
     */
    override fun getChangeRecordsByType(changeType: ProductChangeType): Flow<List<ProductChangeRecordEntity>> {
        return productChangeRecordDao.getChangeRecordsByType(changeType)
    }

    /**
     * 获取指定时间范围内的变化记录
     */
    override fun getChangeRecordsByTimeRange(startTime: Date, endTime: Date): Flow<List<ProductChangeRecordEntity>> {
        return productChangeRecordDao.getChangeRecordsByTimeRange(startTime, endTime)
    }

    /**
     * 清理旧的变化记录
     */
    override suspend fun cleanOldChangeRecords(beforeDate: Date): Int {
        return try {
            productChangeRecordDao.cleanOldRecords(beforeDate)
        } catch (e: Exception) {
            e.printStackTrace()
            0
        }
    }

    /**
     * 获取商品监控统计信息
     */
    override suspend fun getMonitoringStatistics(): Map<String, Int> {
        return try {
            mapOf(
                "totalProducts" to productMonitorDao.getProductCount(),
                "enabledProducts" to productMonitorDao.getEnabledProductCount(),
                "totalChangeRecords" to productChangeRecordDao.getChangeRecordCount()
            )
        } catch (e: Exception) {
            e.printStackTrace()
            emptyMap()
        }
    }
}