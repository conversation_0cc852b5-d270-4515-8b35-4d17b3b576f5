package dev.pigmomo.yhkit2025.service

import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.WindowManager
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.platform.ComposeView
import androidx.core.app.NotificationCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.ViewModelStore
import androidx.lifecycle.ViewModelStoreOwner
import androidx.lifecycle.setViewTreeLifecycleOwner
import androidx.lifecycle.setViewTreeViewModelStoreOwner
import androidx.savedstate.SavedStateRegistry
import androidx.savedstate.SavedStateRegistryController
import androidx.savedstate.SavedStateRegistryOwner
import androidx.savedstate.setViewTreeSavedStateRegistryOwner
import dev.pigmomo.yhkit2025.MainActivity
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.hooks.utils.HttpServerUtils
import dev.pigmomo.yhkit2025.service.productmonitor.MonitoringServiceManager
import dev.pigmomo.yhkit2025.ui.components.FloatingInfoWindow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

/**
 * 后台服务
 * 用于保持应用在后台长时间运行
 */
class BackgroundService : Service(), LifecycleOwner, ViewModelStoreOwner, SavedStateRegistryOwner {
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private var wakeLock: PowerManager.WakeLock? = null
    private val TAG = "BackgroundService"

    // 悬浮窗相关
    private var windowManager: WindowManager? = null
    private var floatingView: ComposeView? = null
    private var params: WindowManager.LayoutParams? = null
    private val _isFloatingWindowShown = MutableStateFlow(false)
    private val isFloatingWindowContentVisible = mutableStateOf(false)

    // Lifecycle and ViewModelStore
    private val lifecycleRegistry = LifecycleRegistry(this)
    override val viewModelStore = ViewModelStore()
    override val lifecycle: Lifecycle get() = lifecycleRegistry

    // SavedStateRegistryOwner implementation
    private val savedStateRegistryController = SavedStateRegistryController.create(this)
    override val savedStateRegistry: SavedStateRegistry get() = savedStateRegistryController.savedStateRegistry

    companion object {
        private const val NOTIFICATION_ID = 1
        private const val CHANNEL_ID = "YHKit_Service_Channel"
        const val ACTION_SHOW_FLOATING_WINDOW = "ACTION_SHOW_FLOATING_WINDOW"
        const val ACTION_HIDE_FLOATING_WINDOW = "ACTION_HIDE_FLOATING_WINDOW"

        // 添加注册信息相关变量
        private val registrationInfoFlow = MutableStateFlow("后台运行中")
        private var formatType: String = ""

        // 用于存储当前服务实例的弱引用
        private var serviceInstance: WeakReference<BackgroundService>? = null

        /**
         * 更新注册信息并立即刷新通知
         */
        fun updateRegistrationInfo(info: String, type: String = "") {
            registrationInfoFlow.value = info
            formatType = type
            Log.d("BackgroundService", "Registration info updated: $info")
            Log.d("BackgroundService", "Format type: $formatType")

            // 立即更新通知
            serviceInstance?.get()?.updateNotificationNow()
        }

        /**
         * 重置注册信息为默认值"后台运行中"
         */
        fun resetRegistrationInfo() {
            registrationInfoFlow.value = "后台运行中"
            Log.d("BackgroundService", "Registration info reset to default")

            // 立即更新通知
            serviceInstance?.get()?.updateNotificationNow()
        }

        /**
         * 获取当前注册信息
         */
        fun getRegistrationInfo(): String {
            return registrationInfoFlow.value
        }

        /**
         * 启动服务
         */
        fun startService(context: Context) {
            val intent = Intent(context, BackgroundService::class.java)
            context.startForegroundService(intent)
        }

        /**
         * 停止服务
         */
        fun stopService(context: Context) {
            val intent = Intent(context, BackgroundService::class.java)
            context.stopService(intent)
        }

        /**
         * 切换悬浮窗显示/隐藏状态
         */
        fun toggleFloatingWindow(context: Context, show: Boolean) {
            val intent = Intent(context, BackgroundService::class.java).apply {
                action = if (show) ACTION_SHOW_FLOATING_WINDOW else ACTION_HIDE_FLOATING_WINDOW
            }
            context.startService(intent)
        }
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Service created")

        // Restore saved state
        savedStateRegistryController.performRestore(null)

        // 初始化 Lifecycle
        lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_CREATE)
        lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_START)
        lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_RESUME)

        // 保存服务实例引用
        serviceInstance = WeakReference(this)

        // 创建通知渠道
        createNotificationChannel()

        // 获取WakeLock
        acquireWakeLock()

        // 启动HTTP服务器
        serviceScope.launch {
            try {
                // 确保HTTP服务器运行
                if (!HttpServerUtils.isServerRunning()) {
                    Log.d(TAG, "Starting HTTP server from service")
                    // 这里需要修改为适配您的HttpServerUtils实现
                    // HttpServerUtils.launchHttpServer(...)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to start HTTP server: ${e.message}")
            }
        }

        // 启动监控调度器
        /*serviceScope.launch {
            try {
                Log.d(TAG, "Starting monitoring scheduler from service")
                val schedulerService = MonitoringServiceManager.getSchedulerService(this@BackgroundService)
                schedulerService.startScheduler()
                Log.i(TAG, "Monitoring scheduler started successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to start monitoring scheduler: ${e.message}")
            }
        }*/

        // 定期执行保活操作
        startKeepAliveJob()
    }

    @SuppressLint("ForegroundServiceType")
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "Service started")

        when (intent?.action) {
            ACTION_SHOW_FLOATING_WINDOW -> showFloatingWindow()
            ACTION_HIDE_FLOATING_WINDOW -> removeFloatingWindow()
        }

        // 创建前台服务通知
        val notification = createNotification()
        startForeground(NOTIFICATION_ID, notification)

        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "Service destroyed")

        // 移除悬浮窗
        removeFloatingWindow(true)

        // 更新 Lifecycle
        lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_PAUSE)
        lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_STOP)
        lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY)
        viewModelStore.clear()

        // 停止监控调度器
        /*try {
            val schedulerService = MonitoringServiceManager.getSchedulerService(this)
            schedulerService.stopScheduler()
            Log.i(TAG, "Monitoring scheduler stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop monitoring scheduler: ${e.message}")
        }*/

        // 释放资源
        releaseWakeLock()
        serviceScope.cancel()
    }

    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            CHANNEL_ID,
            "YH KIT",
            NotificationManager.IMPORTANCE_LOW
        ).apply {
            description = "保持服务在后台运行"
            setShowBadge(false)
        }

        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.createNotificationChannel(channel)
    }

    private fun updateNotificationNow() {
        try {
            val notificationManager =
                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            val notification = createNotification()
            notificationManager.notify(NOTIFICATION_ID, notification)
            Log.d(TAG, "Notification updated immediately with info: ${registrationInfoFlow.value}")
        } catch (e: Exception) {
            Log.e(TAG, "Error updating notification immediately: ${e.message}")
        }
    }

    private fun createNotification(): Notification {
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )

        // 解析注册信息
        val formattedInfo = formatRegistrationInfo(registrationInfoFlow.value, formatType)

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("YH KIT 后台运行中")
            .setContentText(formattedInfo)
            .setStyle(NotificationCompat.BigTextStyle().bigText(formattedInfo))
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            //.setContentIntent(pendingIntent)
            .setOngoing(true)
            .build()
    }

    /**
     * 格式化注册信息以便在通知中显示
     */
    private fun formatRegistrationInfo(info: String, formatType: String = ""): String {
        if (info == "后台运行中") return info

        try {
            // 根据formatType参数判断格式类型
            when (formatType) {
                "progress" -> {
                    // 操作进度信息格式 "totalCount,successCount,failCount,progressPercentage"
                    val parts = info.split(",")
                    if (parts.size >= 4) {
                        val totalCount = parts[0]
                        val successCount = parts[1]
                        val failCount = parts[2]
                        val progressPercentage = parts[3]
                        return "总数: $totalCount | 成功: $successCount | 失败: $failCount | 进度: $progressPercentage%"
                    }
                }

                "query" -> {
                    // 查询信息格式 "totalCount,successCount,failCount,progressPercentage"
                    val parts = info.split(",")
                    if (parts.size >= 3) {
                        val totalCount = parts[0]
                        val successCount = parts[1]
                        val failCount = parts[2]
                        return "总数: $totalCount | 成功: $successCount | 失败: $failCount"
                    }
                }

                "login" -> {
                    // 账号信息格式 "total,loggedIn,loggingIn,timedOut"
                    val parts = info.split(",")
                    if (parts.size >= 4) {
                        val total = parts[0]
                        val loggedIn = parts[1]
                        val loggingIn = parts[2]
                        val timedOut = parts[3]
                        return "总数: $total | 已登录: $loggedIn | 登录中: $loggingIn | 超时: $timedOut"
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error formatting registration info: ${e.message}")
        }

        return info
    }

    private fun acquireWakeLock() {
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK,
            "YHKit:BackgroundServiceWakeLock"
        ).apply {
            acquire(10 * 60 * 1000L) // 10分钟
        }
    }

    private fun releaseWakeLock() {
        wakeLock?.let {
            if (it.isHeld) {
                it.release()
            }
        }
        wakeLock = null
    }

    private fun startKeepAliveJob() {
        serviceScope.launch {
            while (true) {
                try {
                    // 每5分钟执行一次保活操作
                    delay(5 * 60 * 1000L)
                    Log.d(TAG, "Performing keep-alive operation")

                    // 更新通知以保持服务活跃
                    updateNotification()

                    // 检查HTTP服务器状态
                    if (!HttpServerUtils.isServerRunning()) {
                        Log.d(TAG, "Restarting HTTP server")
                        // 这里需要修改为适配您的HttpServerUtils实现
                        // HttpServerUtils.launchHttpServer(...)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error in keep-alive job: ${e.message}")
                    return@launch
                }
            }
        }
    }

    private fun updateNotification() {
        val notification = createNotification()
        val notificationManager =
            getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    /**
     * 显示悬浮窗
     */
    @SuppressLint("ClickableViewAccessibility", "StateFlowValueCalledInComposition")
    private fun showFloatingWindow() {
        if (!Settings.canDrawOverlays(this)) {
            Log.e(TAG, "No overlay permission")
            return
        }

        if (floatingView != null) {
            Log.d(TAG, "Floating window already shown")
            // 如果视图已存在但不可见，则使其可见
            if (!isFloatingWindowContentVisible.value) {
                isFloatingWindowContentVisible.value = true
            }
            return
        }

        _isFloatingWindowShown.value = true
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager

        params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.BOTTOM or Gravity.START
            x = 48
            y = 24
        }

        floatingView = ComposeView(this).apply {
            setViewTreeLifecycleOwner(this@BackgroundService)
            setViewTreeViewModelStoreOwner(this@BackgroundService)
            setViewTreeSavedStateRegistryOwner(this@BackgroundService)
            setContent {
                AnimatedVisibility(
                    visible = isFloatingWindowContentVisible.value,
                    enter = slideInVertically { it },
                    exit = slideOutVertically { it }
                ) {
                    val info by registrationInfoFlow.collectAsState()
                    FloatingInfoWindow(
                        info = info
                    )
                }
            }
        }

        windowManager?.addView(floatingView, params)
        // 使用 post 确保视图已附加，然后启动动画
        floatingView?.post {
            isFloatingWindowContentVisible.value = true
        }
        Log.d(TAG, "Floating window added")
    }

    /**
     * 移除悬浮窗
     */
    private fun removeFloatingWindow(isPermanent: Boolean = false) {
        if (floatingView != null && windowManager != null) {
            // 触发退出动画
            isFloatingWindowContentVisible.value = false

            if (!isPermanent) {
                // 启动一个协程，在动画结束后移除视图
                serviceScope.launch {
                    // 动画默认持续时间约为 300 毫秒
                    delay(300L)
                    // 切换到主线程来操作视图
                    launch(Dispatchers.Main) {
                        if (floatingView != null && windowManager != null) {
                            windowManager?.removeView(floatingView)
                            floatingView = null
                            windowManager = null
                            params = null
                            _isFloatingWindowShown.value = false
                            Log.d(TAG, "Floating window removed")
                        }
                    }
                }
            } else {
                windowManager?.removeView(floatingView)
                floatingView = null
                windowManager = null
                params = null
                _isFloatingWindowShown.value = false
                Log.d(TAG, "Floating window removed")
            }
        }
    }

}






