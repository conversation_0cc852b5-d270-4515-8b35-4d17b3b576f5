package dev.pigmomo.yhkit2025.service.productmonitor

import android.content.Context
import dev.pigmomo.yhkit2025.data.database.AppDatabase
import dev.pigmomo.yhkit2025.data.repository.productmonitor.MonitoringPlanRepository
import dev.pigmomo.yhkit2025.data.repository.productmonitor.MonitoringPlanRepositoryImpl
import dev.pigmomo.yhkit2025.data.repository.productmonitor.ProductMonitorRepository
import dev.pigmomo.yhkit2025.data.repository.productmonitor.ProductMonitorRepositoryImpl

/**
 * 监控服务管理器
 * 用于管理监控相关服务的单例实例
 */
object MonitoringServiceManager {

    @Volatile
    private var productMonitorRepository: ProductMonitorRepository? = null

    @Volatile
    private var monitoringPlanRepository: MonitoringPlanRepository? = null

    @Volatile
    private var taskExecutor: MonitoringTaskExecutor? = null

    @Volatile
    private var schedulerService: MonitoringSchedulerService? = null

    /**
     * 获取商品监控仓库实例
     * @param context 应用上下文
     * @return ProductMonitorRepository实例
     */
    fun getProductMonitorRepository(context: Context): ProductMonitorRepository {
        return productMonitorRepository ?: synchronized(this) {
            productMonitorRepository ?: run {
                val database = AppDatabase.Companion.getDatabase(context)
                ProductMonitorRepositoryImpl(
                    database.productMonitorDao(),
                    database.productChangeRecordDao()
                ).also { productMonitorRepository = it }
            }
        }
    }

    /**
     * 获取监控计划仓库实例
     * @param context 应用上下文
     * @return MonitoringPlanRepository实例
     */
    fun getMonitoringPlanRepository(context: Context): MonitoringPlanRepository {
        return monitoringPlanRepository ?: synchronized(this) {
            monitoringPlanRepository ?: run {
                val database = AppDatabase.Companion.getDatabase(context)
                MonitoringPlanRepositoryImpl(
                    database.monitoringPlanDao()
                ).also { monitoringPlanRepository = it }
            }
        }
    }

    /**
     * 获取监控任务执行器实例
     * @param context 应用上下文
     * @return MonitoringTaskExecutor实例
     */
    fun getTaskExecutor(context: Context): MonitoringTaskExecutor {
        return taskExecutor ?: synchronized(this) {
            taskExecutor ?: MonitoringTaskExecutor(
                getProductMonitorRepository(context),
                getMonitoringPlanRepository(context)
            ).also { taskExecutor = it }
        }
    }

    /**
     * 获取监控调度服务实例
     * @param context 应用上下文
     * @return MonitoringSchedulerService实例
     */
    fun getSchedulerService(context: Context): MonitoringSchedulerService {
        return schedulerService ?: synchronized(this) {
            schedulerService ?: MonitoringSchedulerService(
                getMonitoringPlanRepository(context),
                getTaskExecutor(context)
            ).also { schedulerService = it }
        }
    }

    /**
     * 清理所有实例（用于测试或重置）
     */
    fun clearInstances() {
        synchronized(this) {
            schedulerService?.stopScheduler()
            taskExecutor = null
            schedulerService = null
            productMonitorRepository = null
            monitoringPlanRepository = null
        }
    }

    /**
     * 检查服务是否已初始化
     * @return 是否已初始化
     */
    fun isInitialized(): Boolean {
        return taskExecutor != null && schedulerService != null
    }

    /**
     * 获取服务状态信息
     * @return 状态信息Map
     */
    fun getServiceStatus(): Map<String, Any> {
        return mapOf(
            "productMonitorRepositoryInitialized" to (productMonitorRepository != null),
            "monitoringPlanRepositoryInitialized" to (monitoringPlanRepository != null),
            "taskExecutorInitialized" to (taskExecutor != null),
            "schedulerServiceInitialized" to (schedulerService != null),
            "schedulerRunning" to (schedulerService?.isSchedulerRunning() ?: false)
        )
    }

    /**
     * 初始化所有服务
     * @param context 应用上下文
     */
    fun initializeServices(context: Context) {
        getProductMonitorRepository(context)
        getMonitoringPlanRepository(context)
        getTaskExecutor(context)
        getSchedulerService(context)
    }
}