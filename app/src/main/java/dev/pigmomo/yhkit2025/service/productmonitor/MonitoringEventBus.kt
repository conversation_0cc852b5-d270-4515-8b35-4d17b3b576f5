package dev.pigmomo.yhkit2025.service.productmonitor

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * 监控事件总线
 * 用于在监控任务执行完成后通知UI更新
 */
object MonitoringEventBus {
    
    /**
     * 监控事件类型
     */
    sealed class MonitoringEvent {
        /**
         * 任务执行完成事件
         * @param planId 监控计划ID
         * @param result 完整的执行结果
         */
        data class TaskExecuted(
            val planId: Int,
            val result: MonitoringTaskResult
        ) : MonitoringEvent()

        /**
         * 批量任务执行完成事件
         * @param results 执行结果列表 (planId, MonitoringTaskResult)
         */
        data class BatchTasksExecuted(
            val results: List<Pair<Int, MonitoringTaskResult>>
        ) : MonitoringEvent()
        
        /**
         * 调度器状态变化事件
         * @param isRunning 调度器是否正在运行
         */
        data class SchedulerStatusChanged(
            val isRunning: Boolean
        ) : MonitoringEvent()
        
        /**
         * 监控计划数据更新事件
         * 用于通知UI刷新监控计划列表
         */
        object PlansDataUpdated : MonitoringEvent()
    }
    
    private val _events = MutableSharedFlow<MonitoringEvent>(
        replay = 0,
        extraBufferCapacity = 10
    )
    
    /**
     * 事件流
     */
    val events: SharedFlow<MonitoringEvent> = _events.asSharedFlow()
    
    /**
     * 发送任务执行完成事件
     * @param planId 监控计划ID
     * @param result 完整的执行结果
     */
    suspend fun emitTaskExecuted(planId: Int, result: MonitoringTaskResult) {
        _events.emit(MonitoringEvent.TaskExecuted(planId, result))
        // 同时发送数据更新事件
        _events.emit(MonitoringEvent.PlansDataUpdated)
    }

    /**
     * 发送批量任务执行完成事件
     * @param results 执行结果列表 (planId, MonitoringTaskResult)
     */
    suspend fun emitBatchTasksExecuted(results: List<Pair<Int, MonitoringTaskResult>>) {
        _events.emit(MonitoringEvent.BatchTasksExecuted(results))
        // 同时发送数据更新事件
        _events.emit(MonitoringEvent.PlansDataUpdated)
    }
    
    /**
     * 发送调度器状态变化事件
     * @param isRunning 调度器是否正在运行
     */
    suspend fun emitSchedulerStatusChanged(isRunning: Boolean) {
        _events.emit(MonitoringEvent.SchedulerStatusChanged(isRunning))
    }
    
    /**
     * 发送监控计划数据更新事件
     */
    suspend fun emitPlansDataUpdated() {
        _events.emit(MonitoringEvent.PlansDataUpdated)
    }
    
    /**
     * 尝试发送事件（不会抛出异常）
     * @param event 要发送的事件
     * @return 是否发送成功
     */
    fun tryEmit(event: MonitoringEvent): Boolean {
        return _events.tryEmit(event)
    }
}
