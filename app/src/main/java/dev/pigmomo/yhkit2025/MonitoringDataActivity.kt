package dev.pigmomo.yhkit2025

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.lifecycle.ViewModelProvider
import dev.pigmomo.yhkit2025.service.productmonitor.MonitoringServiceManager
import dev.pigmomo.yhkit2025.ui.screens.MonitoringDataScreen
import dev.pigmomo.yhkit2025.ui.theme.Yhkit2025Theme
import dev.pigmomo.yhkit2025.viewmodel.MonitoringDataViewModel

/**
 * 监控数据展示Activity
 * 用于展示所有监控商品的详细数据和变化记录
 */
class MonitoringDataActivity : ComponentActivity() {

    private lateinit var viewModel: MonitoringDataViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 初始化Repository和ViewModel
        val monitoringPlanRepository =
            MonitoringServiceManager.getMonitoringPlanRepository(applicationContext)
        val productMonitorRepository =
            MonitoringServiceManager.getProductMonitorRepository(applicationContext)

        viewModel = ViewModelProvider(
            this,
            MonitoringDataViewModel.Factory(
                application,
                monitoringPlanRepository,
                productMonitorRepository
            )
        )[MonitoringDataViewModel::class.java]

        setContent {
            Yhkit2025Theme(dynamicColor = false) {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MonitoringDataScreen(
                        viewModel = viewModel,
                        onNavigateBack = {
                            finish()
                        }
                    )
                }
            }
        }
    }
}
