package dev.pigmomo.yhkit2025.ui.screens

import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeType
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.viewmodel.MonitoringDataViewModel
import kotlinx.coroutines.launch
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import java.text.SimpleDateFormat
import java.util.*

/**
 * 监控数据展示Screen
 * 用于展示所有监控商品的详细数据和变化记录
 * @param viewModel 监控数据视图模型
 * @param onNavigateBack 返回导航回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MonitoringDataScreen(
    viewModel: MonitoringDataViewModel,
    onNavigateBack: () -> Unit
) {
    // 从ViewModel获取状态
    val monitoredProducts by viewModel.monitoredProducts.collectAsState()
    val selectedProduct by viewModel.selectedProduct.collectAsState()
    val selectedProductChangeRecords by viewModel.selectedProductChangeRecords.collectAsState()
    val isLoading by viewModel.isLoading
    val errorMessage by viewModel.errorMessage

    // 从ViewModel获取格式化器
    val dateFormat = viewModel.dateFormat
    val fullDateFormat = viewModel.fullDateFormat

    // 显示错误信息
    errorMessage?.let { message ->
        LaunchedEffect(message) {
            viewModel.clearErrorMessage()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Monitoring Data") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.refreshData() }) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White,
                    actionIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            // 显示商品列表
            ProductListScreen(
                products = monitoredProducts,
                viewModel = viewModel,
                modifier = Modifier.padding(paddingValues)
            )
        }
    }
}

/**
 * 商品列表页面
 */
@Composable
fun ProductListScreen(
    products: List<ProductMonitorEntity>,
    viewModel: MonitoringDataViewModel,
    modifier: Modifier = Modifier
) {
    // 管理展开的商品ID（同时只能展开一个）
    var expandedProductId by remember { mutableStateOf<String?>(null) }
    // 管理每个商品的变化记录
    var productChangeRecords by remember { mutableStateOf(mapOf<String, List<ProductChangeRecordEntity>>()) }
    // 协程作用域
    val coroutineScope = rememberCoroutineScope()

    // 如果有展开的商品，显示全屏视图
    expandedProductId?.let { productId ->
        val product = products.find { it.id == productId }
        if (product != null) {
            FullScreenProductView(
                product = product,
                changeRecords = productChangeRecords[productId] ?: emptyList(),
                onClose = {
                    expandedProductId = null
                    productChangeRecords = productChangeRecords - productId
                },
                dateFormat = viewModel.dateFormat,
                fullDateFormat = viewModel.fullDateFormat,
                modifier = modifier
            )
            return
        }
    }

    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(products) { product ->
            ProductListItem(
                product = product,
                isExpanded = false, // 列表中不显示展开状态
                changeRecords = emptyList(), // 列表中不显示变化记录
                onToggleExpand = { productId ->
                    // 展开时进入全屏模式
                    expandedProductId = productId
                    // 加载变化记录
                    coroutineScope.launch {
                        try {
                            val records = viewModel.getProductChangeRecords(product.id, product.shopId)
                            records.collect { recordList ->
                                productChangeRecords = productChangeRecords + (productId to recordList.sortedByDescending { it.changeTime })
                            }
                        } catch (e: Exception) {
                            Log.e("ProductListScreen", "Failed to load change records for product ${product.id}", e)
                        }
                    }
                },
                dateFormat = viewModel.dateFormat,
                fullDateFormat = viewModel.fullDateFormat
            )
        }
    }
}

/**
 * 商品列表项组件
 */
@SuppressLint("DefaultLocale")
@Composable
fun ProductListItem(
    product: ProductMonitorEntity,
    isExpanded: Boolean,
    changeRecords: List<ProductChangeRecordEntity>,
    onToggleExpand: (String) -> Unit,
    dateFormat: SimpleDateFormat,
    fullDateFormat: SimpleDateFormat
) {

    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(max = 500.dp)
    ) {
        Column {
            // 主要信息区域 - 点击展开/收起
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onToggleExpand(product.id) }
                    .padding(horizontal = 12.dp, vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 商品标题
                Text(
                    text = product.title,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    modifier = Modifier.horizontalScroll(rememberScrollState())
                )

                // 展开图标
                Icon(
                    imageVector = Icons.Default.KeyboardArrowDown,
                    contentDescription = "展开详情",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // SKU、库存、店铺信息行
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 12.dp, vertical = 2.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.Bottom
            ) {
                Text(
                    text = "SKU: ${product.id} ${if (product.originalSkuCode.isNotEmpty() && product.originalSkuCode != product.id) "(${product.originalSkuCode})" else ""}",
                    fontSize = 11.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.alignByBaseline()
                )
                Text(
                    text = "库存: ${if (product.stockNum > 0) "${product.stockNum / 100}" else "未知"}",
                    fontSize = 11.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.alignByBaseline()
                )
                if (product.shopId.isNotEmpty()) {
                    Text(
                        text = "店铺: ${product.shopId}",
                        fontSize = 11.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.alignByBaseline()
                    )
                }
            }

            // 价格和状态标签行
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 12.dp, vertical = 4.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 价格信息
                if (product.currentPrice > 0) {
                    Text(
                        text = "¥${String.format("%.2f", product.currentPrice / 100.0)}",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                } else {
                    Text(
                        text = "暂无价格",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // 状态标签组
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 秒杀标签
                    if (product.isSeckill == 1) {
                        Surface(
                            color = Color(0xFFFF9800),
                            shape = RoundedCornerShape(4.dp)
                        ) {
                            Text(
                                text = "秒杀",
                                color = Color.White,
                                fontSize = 10.sp,
                                lineHeight = 10.sp,
                                modifier = Modifier.padding(horizontal = 6.dp)
                            )
                        }
                    }

                    // 可用性状态标签
                    when {
                        product.available == 0 -> {
                            Surface(
                                color = Color(0xFF9E9E9E),
                                shape = RoundedCornerShape(4.dp)
                            ) {
                                Text(
                                    text = "下架",
                                    color = Color.White,
                                    fontSize = 10.sp,
                                    lineHeight = 10.sp,
                                    modifier = Modifier.padding(horizontal = 6.dp)
                                )
                            }
                        }

                        product.canNotBuy -> {
                            Surface(
                                color = Color(0xFFF44336),
                                shape = RoundedCornerShape(4.dp)
                            ) {
                                Text(
                                    text = "不可购买",
                                    color = Color.White,
                                    fontSize = 10.sp,
                                    lineHeight = 10.sp,
                                    modifier = Modifier.padding(horizontal = 6.dp)
                                )
                            }
                        }

                        product.stockNum == 0 -> {
                            Surface(
                                color = Color(0xFFF44336),
                                shape = RoundedCornerShape(4.dp)
                            ) {
                                Text(
                                    text = "缺货",
                                    color = Color.White,
                                    fontSize = 10.sp,
                                    lineHeight = 10.sp,
                                    modifier = Modifier.padding(horizontal = 6.dp)
                                )
                            }
                        }

                        product.available == 1 && product.stockNum > 0 -> {
                            Surface(
                                color = Color(0xFF4CAF50),
                                shape = RoundedCornerShape(4.dp)
                            ) {
                                Text(
                                    text = "有货",
                                    color = Color.White,
                                    fontSize = 10.sp,
                                    lineHeight = 10.sp,
                                    modifier = Modifier.padding(horizontal = 6.dp)
                                )
                            }
                        }
                    }

                    // 限购标签
                    if (product.restrictLimit > 0) {
                        Surface(
                            color = Color(0xFFFF9800),
                            shape = RoundedCornerShape(4.dp)
                        ) {
                            Text(
                                text = "限购${product.restrictLimit}",
                                color = Color.White,
                                fontSize = 10.sp,
                                lineHeight = 10.sp,
                                modifier = Modifier.padding(horizontal = 6.dp)
                            )
                        }
                    }
                }
            }

            // 展开的详细信息
            if (isExpanded) {
                // 详细信息网格
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 12.dp, vertical = 6.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 监控状态和变化记录
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Surface(
                            color = if (product.isMonitoringEnabled) Color(0xFF4CAF50).copy(alpha = 0.1f) else Color(0xFFF44336).copy(alpha = 0.1f),
                            shape = RoundedCornerShape(6.dp),
                            modifier = Modifier.weight(1f)
                        ) {
                            Text(
                                text = if (product.isMonitoringEnabled) "监控中 上次更新: ${dateFormat.format(product.lastUpdateTime)}" else "已停止 上次更新: ${dateFormat.format(product.lastUpdateTime)}",
                                fontSize = 10.sp,
                                color = if (product.isMonitoringEnabled) Color(0xFF4CAF50) else Color(0xFFF44336),
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                            )
                        }

                        // 变化记录数量
                        if (changeRecords.isNotEmpty()) {
                            Spacer(modifier = Modifier.width(8.dp))
                            Surface(
                                color = Color(0xFF2196F3).copy(alpha = 0.1f),
                                shape = RoundedCornerShape(6.dp)
                            ) {
                                Text(
                                    text = "${changeRecords.size}条变化",
                                    fontSize = 10.sp,
                                    color = Color(0xFF2196F3),
                                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                )
                            }
                        }
                    }

                    // 变化记录列表
                    if (changeRecords.isNotEmpty()) {
                        // 显示最近的变化记录
                        changeRecords.forEach { record ->
                            ChangeRecordCompactItem(
                                record = record,
                                dateFormat = fullDateFormat
                            )
                            Spacer(modifier = Modifier.height(2.dp))
                        }
                    }
                }
            }

        }
    }
}

/**
 * 紧凑的变化记录项组件
 */
@Composable
fun ChangeRecordCompactItem(
    record: ProductChangeRecordEntity,
    dateFormat: SimpleDateFormat
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            modifier = Modifier.weight(1f),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 变化类型标签
            Surface(
                color = when (record.changeType) {
                    ProductChangeType.PRICE_CHANGE -> Color(0xFF2196F3)
                    ProductChangeType.STOCK_CHANGE -> Color(0xFF4CAF50)
                    ProductChangeType.AVAILABILITY_CHANGE -> Color(0xFFFF9800)
                    ProductChangeType.INFO_CHANGE -> Color(0xFF9C27B0)
                    ProductChangeType.SECKILL_STATUS_CHANGE -> Color(0xFFF44336)
                    ProductChangeType.RESTRICT_CHANGE -> Color(0xFFFFCDD2)
                    else -> Color(0xFF607D8B)
                },
                shape = RoundedCornerShape(4.dp)
            ) {
                Text(
                    text = when (record.changeType) {
                        ProductChangeType.PRICE_CHANGE -> "价格"
                        ProductChangeType.STOCK_CHANGE -> "库存"
                        ProductChangeType.AVAILABILITY_CHANGE -> "可用性"
                        ProductChangeType.INFO_CHANGE -> "信息"
                        ProductChangeType.SECKILL_STATUS_CHANGE -> "秒杀"
                        ProductChangeType.RESTRICT_CHANGE -> "限购"
                        else -> "其他"
                    },
                    color = Color.White,
                    fontSize = 8.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(horizontal = 4.dp, vertical = 2.dp)
                )
            }

            // 变化内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                if (record.oldValue.isNotEmpty() || record.newValue.isNotEmpty()) {
                    Text(
                        text = "${record.oldValue} → ${record.newValue}",
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.primary,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        fontWeight = FontWeight.Medium
                    )
                } else if (record.changeDescription.isNotEmpty()) {
                    Text(
                        text = record.changeDescription,
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }

        // 时间
        Text(
            text = dateFormat.format(record.changeTime),
            fontSize = 9.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 全屏商品详情视图
 */
@SuppressLint("DefaultLocale")
@Composable
fun FullScreenProductView(
    product: ProductMonitorEntity,
    changeRecords: List<ProductChangeRecordEntity>,
    onClose: () -> Unit,
    dateFormat: SimpleDateFormat,
    fullDateFormat: SimpleDateFormat,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        // 顶部关闭按钮
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "商品详情",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )

            IconButton(onClick = onClose) {
                Icon(
                    imageVector = Icons.Default.Clear,
                    contentDescription = "关闭",
                    tint = MaterialTheme.colorScheme.onSurface
                )
            }
        }

        // 可滚动的内容区域
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                // 商品基本信息卡片
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // 商品标题
                        Text(
                            text = product.title,
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold
                        )

                        // SKU 信息
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "SKU: ${product.id}",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            if (product.originalSkuCode.isNotEmpty() && product.originalSkuCode != product.id) {
                                Text(
                                    text = "原始SKU: ${product.originalSkuCode}",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }

                        // 价格和库存信息
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Column {
                                Text(
                                    text = "当前价格",
                                    style = MaterialTheme.typography.labelMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                                Text(
                                    text = if (product.currentPrice > 0) "¥${String.format("%.2f", product.currentPrice / 100.0)}" else "未知",
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }

                            Column {
                                Text(
                                    text = "库存数量",
                                    style = MaterialTheme.typography.labelMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                                Text(
                                    text = if (product.stockNum > 0) "${product.stockNum / 100}" else "未知",
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.Bold,
                                    color = if (product.stockNum > 0) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error
                                )
                            }
                        }

                        // 店铺信息
                        Text(
                            text = "店铺: ${product.shopId}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )

                        // 监控状态
                        Surface(
                            color = if (product.isMonitoringEnabled) Color(0xFF4CAF50).copy(alpha = 0.1f) else Color(0xFFF44336).copy(alpha = 0.1f),
                            shape = RoundedCornerShape(8.dp),
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text(
                                text = if (product.isMonitoringEnabled) "监控中 - 上次更新: ${dateFormat.format(product.lastUpdateTime)}" else "已停止 - 上次更新: ${dateFormat.format(product.lastUpdateTime)}",
                                style = MaterialTheme.typography.bodyMedium,
                                color = if (product.isMonitoringEnabled) Color(0xFF4CAF50) else Color(0xFFF44336),
                                modifier = Modifier.padding(12.dp)
                            )
                        }
                    }
                }
            }

            // 变化记录标题
            if (changeRecords.isNotEmpty()) {
                item {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "变化记录",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Surface(
                            color = Color(0xFF2196F3).copy(alpha = 0.1f),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            Text(
                                text = "${changeRecords.size}条记录",
                                style = MaterialTheme.typography.labelMedium,
                                color = Color(0xFF2196F3),
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                            )
                        }
                    }
                }
            }

            // 变化记录列表
            items(changeRecords) { record ->
                FullScreenChangeRecordItem(
                    record = record,
                    dateFormat = fullDateFormat
                )
            }

            // 底部间距
            item {
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

/**
 * 全屏模式下的变化记录项组件
 */
@Composable
fun FullScreenChangeRecordItem(
    record: ProductChangeRecordEntity,
    dateFormat: SimpleDateFormat
) {
    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 顶部：变化类型和时间
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 变化类型标签
                Surface(
                    color = when (record.changeType) {
                        ProductChangeType.PRICE_CHANGE -> Color(0xFF2196F3)
                        ProductChangeType.STOCK_CHANGE -> Color(0xFF4CAF50)
                        ProductChangeType.AVAILABILITY_CHANGE -> Color(0xFFFF9800)
                        ProductChangeType.INFO_CHANGE -> Color(0xFF9C27B0)
                        ProductChangeType.SECKILL_STATUS_CHANGE -> Color(0xFFF44336)
                        ProductChangeType.RESTRICT_CHANGE -> Color(0xFFFFCDD2)
                        else -> Color(0xFF607D8B)
                    },
                    shape = RoundedCornerShape(6.dp)
                ) {
                    Text(
                        text = when (record.changeType) {
                            ProductChangeType.PRICE_CHANGE -> "价格变化"
                            ProductChangeType.STOCK_CHANGE -> "库存变化"
                            ProductChangeType.AVAILABILITY_CHANGE -> "可用性变化"
                            ProductChangeType.INFO_CHANGE -> "信息变化"
                            ProductChangeType.SECKILL_STATUS_CHANGE -> "秒杀状态变化"
                            ProductChangeType.RESTRICT_CHANGE -> "限购变化"
                            else -> "其他变化"
                        },
                        color = Color.White,
                        style = MaterialTheme.typography.labelMedium,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }

                // 时间
                Text(
                    text = dateFormat.format(record.changeTime),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // 变化内容
            if (record.oldValue.isNotEmpty() || record.newValue.isNotEmpty()) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    if (record.oldValue.isNotEmpty()) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Text(
                                text = "原值:",
                                style = MaterialTheme.typography.labelMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Text(
                                text = record.oldValue,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.error
                            )
                        }
                    }

                    if (record.newValue.isNotEmpty()) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Text(
                                text = "新值:",
                                style = MaterialTheme.typography.labelMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Text(
                                text = record.newValue,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                }
            } else if (record.changeDescription.isNotEmpty()) {
                Text(
                    text = record.changeDescription,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
    }
}
